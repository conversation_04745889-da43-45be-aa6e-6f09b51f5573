#!/bin/bash

# Enhanced Sequential GLUE Tasks Runner with YAML Parameter Modification
# This script modifies YAML templates with custom parameters and runs GLUE fine-tuning tasks
#
# Usage: ./run_glue_edited.sh [OPTIONS]
# Options:
#   Method 1 - Using parameter file:
#   -y, --yaml-config FILE             YAML configuration file containing parameters (recommended)
#
#   Method 2 - Using individual parameters:
#   -t, --tokenizer-name PATH          Tokenizer name/path (required if not using --yaml-config)
#   -p, --pretrained-model-name NAME   Pretrained model name (required if not using --yaml-config)
#   -c, --checkpoint-path PATH         Starting checkpoint load path (required if not using --yaml-config)
#   -f, --checkpoint-folder PATH       Local pretrain checkpoint folder (required if not using --yaml-config)
#   -r, --base-run-name NAME           Base run name (required if not using --yaml-config)
#   -m, --mnli-checkpoint PATH         MNLI checkpoint path for mrpc/rte/stsb (optional, auto-generated if not provided)
#
#   Common options:
#   -s, --tasks TASKS                  Comma-separated list of tasks to run (optional, runs all if not specified)
#   -l, --log-file FILE                Log file path (default: glue_tasks_log.txt)
#   -g, --gpus DEVICES                 CUDA visible devices (default: 0,1,2,3)
#   -h, --help                         Show this help message
#
# Example with YAML config:
#   ./run_glue_edited.sh --yaml-config my_config.yaml --tasks mnli,cola,mrpc
#
# Example with individual parameters:
#   ./run_glue_edited.sh \
#     --tokenizer-name "/path/to/tokenizer" \
#     --pretrained-model-name "FacebookAI/xlm-roberta-large" \
#     --checkpoint-path "/path/to/checkpoint.pt" \
#     --checkpoint-folder "/path/to/checkpoint/folder" \
#     --base-run-name "my-custom-run" \
#     --tasks mnli,cola,mrpc

# Function to display help
show_help() {
    echo "Enhanced Sequential GLUE Tasks Runner with YAML Parameter Modification"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  Method 1 - Using parameter file:"
    echo "  -y, --yaml-config FILE             YAML configuration file containing parameters (recommended)"
    echo ""
    echo "  Method 2 - Using individual parameters:"
    echo "  -t, --tokenizer-name PATH          Tokenizer name/path (required if not using --yaml-config)"
    echo "  -p, --pretrained-model-name NAME   Pretrained model name (required if not using --yaml-config)"
    echo "  -c, --checkpoint-path PATH         Starting checkpoint load path (required if not using --yaml-config)"
    echo "  -f, --checkpoint-folder PATH       Local pretrain checkpoint folder (required if not using --yaml-config)"
    echo "  -r, --base-run-name NAME           Base run name (required if not using --yaml-config)"
    echo "  -m, --mnli-checkpoint PATH         MNLI checkpoint path for mrpc/rte/stsb (optional, auto-generated if not provided)"
    echo ""
    echo "  Common options:"
    echo "  -s, --tasks TASKS                  Comma-separated list of tasks to run (optional, runs all if not specified)"
    echo "  -l, --log-file FILE                Log file path (default: glue_tasks_log.txt)"
    echo "  -g, --gpus DEVICES                 CUDA visible devices (default: 0,1,2,3)"
    echo "  -h, --help                         Show this help message"
    echo ""
    echo "Available tasks: mnli, cola, mrpc, qnli, qqp, rte, sst2, stsb"
    echo ""
    echo "Example with YAML config:"
    echo "  $0 --yaml-config my_config.yaml --tasks mnli,cola,mrpc"
    echo ""
    echo "Example with individual parameters:"
    echo "  $0 \\"
    echo "    --tokenizer-name \"/path/to/tokenizer\" \\"
    echo "    --pretrained-model-name \"FacebookAI/xlm-roberta-large\" \\"
    echo "    --checkpoint-path \"/path/to/checkpoint.pt\" \\"
    echo "    --checkpoint-folder \"/path/to/checkpoint/folder\" \\"
    echo "    --base-run-name \"my-custom-run\" \\"
    echo "    --tasks mnli,cola,mrpc"
    exit 0
}

# Default values
YAML_CONFIG_FILE=""
TOKENIZER_NAME=""
PRETRAINED_MODEL_NAME=""
CHECKPOINT_PATH=""
CHECKPOINT_FOLDER=""
BASE_RUN_NAME=""
MNLI_CHECKPOINT=""
LOG_FILE="glue_tasks_log.txt"
SELECTED_TASKS=""
CUDA_DEVICES="0,1,2,3"

# Paths
WORKSPACE_ROOT="/home/<USER>/notebooks/ModernBert_pretrain"
TEMPLATE_DIR="${WORKSPACE_ROOT}/yamls/finetuning/glue/task_templates"
TMP_DIR="${WORKSPACE_ROOT}/yamls/finetuning/glue/tmp"

# All available tasks
ALL_TASKS=("mnli" "cola" "mrpc" "qnli" "qqp" "rte" "sst2" "stsb")

# Tasks that should use MNLI checkpoint
MNLI_DEPENDENT_TASKS=("mrpc" "rte" "stsb")

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -y|--yaml-config)
            YAML_CONFIG_FILE="$2"
            shift 2
            ;;
        -t|--tokenizer-name)
            TOKENIZER_NAME="$2"
            shift 2
            ;;
        -p|--pretrained-model-name)
            PRETRAINED_MODEL_NAME="$2"
            shift 2
            ;;
        -c|--checkpoint-path)
            CHECKPOINT_PATH="$2"
            shift 2
            ;;
        -f|--checkpoint-folder)
            CHECKPOINT_FOLDER="$2"
            shift 2
            ;;
        -r|--base-run-name)
            BASE_RUN_NAME="$2"
            shift 2
            ;;
        -m|--mnli-checkpoint)
            MNLI_CHECKPOINT="$2"
            shift 2
            ;;
        -s|--tasks)
            SELECTED_TASKS="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -g|--gpus)
            CUDA_DEVICES="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to parse YAML config file
parse_yaml_config() {
    local config_file="$1"

    if [[ ! -f "$config_file" ]]; then
        echo "Error: YAML config file '$config_file' not found."
        exit 1
    fi

    echo "Parsing YAML config file: $config_file"

    # Parse YAML using a simple approach (assumes clean YAML format)
    # This is a basic parser - for production use, consider using yq or python
    while IFS= read -r line; do
        # Skip comments and empty lines
        [[ "$line" =~ ^[[:space:]]*# ]] && continue
        [[ -z "${line// }" ]] && continue

        # Extract key-value pairs
        if [[ "$line" =~ ^[[:space:]]*([^:]+):[[:space:]]*(.*)$ ]]; then
            key="${BASH_REMATCH[1]// /}"  # Remove spaces
            value="${BASH_REMATCH[2]}"

            # Remove quotes if present
            value="${value#\"}"
            value="${value%\"}"
            value="${value#\'}"
            value="${value%\'}"

            case "$key" in
                tokenizer_name)
                    [[ -z "$TOKENIZER_NAME" ]] && TOKENIZER_NAME="$value"
                    ;;
                pretrained_model_name)
                    [[ -z "$PRETRAINED_MODEL_NAME" ]] && PRETRAINED_MODEL_NAME="$value"
                    ;;
                starting_checkpoint_load_path|checkpoint_path)
                    [[ -z "$CHECKPOINT_PATH" ]] && CHECKPOINT_PATH="$value"
                    ;;
                local_pretrain_checkpoint_folder|checkpoint_folder)
                    [[ -z "$CHECKPOINT_FOLDER" ]] && CHECKPOINT_FOLDER="$value"
                    ;;
                base_run_name)
                    [[ -z "$BASE_RUN_NAME" ]] && BASE_RUN_NAME="$value"
                    ;;
                mnli_checkpoint)
                    [[ -z "$MNLI_CHECKPOINT" ]] && MNLI_CHECKPOINT="$value"
                    ;;
                tasks)
                    [[ -z "$SELECTED_TASKS" ]] && SELECTED_TASKS="$value"
                    ;;
                log_file)
                    [[ "$LOG_FILE" == "glue_tasks_log.txt" ]] && LOG_FILE="$value"
                    ;;
                cuda_devices|gpus)
                    [[ "$CUDA_DEVICES" == "0,1,2,3" ]] && CUDA_DEVICES="$value"
                    ;;
            esac
        fi
    done < "$config_file"

    echo "YAML config parsed successfully."
}

# Parse YAML config if provided
if [[ -n "$YAML_CONFIG_FILE" ]]; then
    parse_yaml_config "$YAML_CONFIG_FILE"
fi

# Validate required parameters
missing_params=()

if [[ -z "$TOKENIZER_NAME" ]]; then
    missing_params+=("tokenizer_name")
fi

if [[ -z "$PRETRAINED_MODEL_NAME" ]]; then
    missing_params+=("pretrained_model_name")
fi

if [[ -z "$CHECKPOINT_PATH" ]]; then
    missing_params+=("checkpoint_path")
fi

if [[ -z "$CHECKPOINT_FOLDER" ]]; then
    missing_params+=("checkpoint_folder")
fi

if [[ -z "$BASE_RUN_NAME" ]]; then
    missing_params+=("base_run_name")
fi

# Check if any required parameters are missing
if [[ ${#missing_params[@]} -gt 0 ]]; then
    echo "Error: Missing required parameters: ${missing_params[*]}"
    echo ""
    if [[ -n "$YAML_CONFIG_FILE" ]]; then
        echo "Please ensure your YAML config file contains all required parameters:"
        for param in "${missing_params[@]}"; do
            echo "  $param: <value>"
        done
    else
        echo "Please provide the missing parameters using command line options or use --yaml-config."
        echo "Required parameters can be provided via:"
        echo "  --tokenizer-name, --pretrained-model-name, --checkpoint-path,"
        echo "  --checkpoint-folder, --base-run-name"
        echo ""
        echo "Or use --yaml-config to specify a YAML file containing these parameters."
    fi
    echo ""
    echo "Use --help for usage information"
    exit 1
fi

if [[ ! -d "$TEMPLATE_DIR" ]]; then
    echo "Error: Template directory '$TEMPLATE_DIR' does not exist."
    exit 1
fi

# Auto-generate MNLI checkpoint path if not provided
if [[ -z "$MNLI_CHECKPOINT" ]]; then
    MNLI_CHECKPOINT="./local-finetune-checkpoints/${BASE_RUN_NAME}-mnli-finetuning/task=mnli/seed=19/latest-rank0.pt"
fi

# Initialize log file
echo "=== GLUE Tasks Execution Log with YAML Modification ===" > "$LOG_FILE"
echo "Started at: $(date)" >> "$LOG_FILE"
echo "Template directory: $TEMPLATE_DIR" >> "$LOG_FILE"
echo "Temporary directory: $TMP_DIR" >> "$LOG_FILE"
echo "Log file: $LOG_FILE" >> "$LOG_FILE"
echo "CUDA devices: $CUDA_DEVICES" >> "$LOG_FILE"
if [[ -n "$YAML_CONFIG_FILE" ]]; then
    echo "YAML config file: $YAML_CONFIG_FILE" >> "$LOG_FILE"
fi
echo "Parameters:" >> "$LOG_FILE"
echo "  Tokenizer name: $TOKENIZER_NAME" >> "$LOG_FILE"
echo "  Pretrained model name: $PRETRAINED_MODEL_NAME" >> "$LOG_FILE"
echo "  Checkpoint path: $CHECKPOINT_PATH" >> "$LOG_FILE"
echo "  Checkpoint folder: $CHECKPOINT_FOLDER" >> "$LOG_FILE"
echo "  Base run name: $BASE_RUN_NAME" >> "$LOG_FILE"
echo "  MNLI checkpoint: $MNLI_CHECKPOINT" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

# Function to log messages to both console and file
log_message() {
    local message="$1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

# Function to create temporary directory and copy templates
setup_tmp_directory() {
    log_message "Setting up temporary directory..."

    # Remove existing tmp directory if it exists
    if [[ -d "$TMP_DIR" ]]; then
        log_message "Removing existing temporary directory: $TMP_DIR"
        rm -rf "$TMP_DIR"
    fi

    # Create new tmp directory
    log_message "Creating temporary directory: $TMP_DIR"
    mkdir -p "$TMP_DIR"

    # Copy all YAML files from templates to tmp
    log_message "Copying YAML templates to temporary directory..."
    for task in "${ALL_TASKS[@]}"; do
        template_file="${TEMPLATE_DIR}/${task}.yaml"
        tmp_file="${TMP_DIR}/${task}.yaml"

        if [[ -f "$template_file" ]]; then
            cp "$template_file" "$tmp_file"
            log_message "  Copied: ${task}.yaml"
        else
            log_message "  WARNING: Template file not found: $template_file"
        fi
    done

    log_message "Temporary directory setup complete."
    log_message ""
}

# Function to modify YAML parameters
modify_yaml_file() {
    local task="$1"
    local yaml_file="${TMP_DIR}/${task}.yaml"

    if [[ ! -f "$yaml_file" ]]; then
        log_message "ERROR: YAML file not found: $yaml_file"
        return 1
    fi

    log_message "Modifying YAML parameters for task: $task"

    # Escape special characters for sed
    local escaped_tokenizer_name=$(printf '%s\n' "$TOKENIZER_NAME" | sed 's/[[\.*^$()+?{|]/\\&/g')
    local escaped_pretrained_model_name=$(printf '%s\n' "$PRETRAINED_MODEL_NAME" | sed 's/[[\.*^$()+?{|]/\\&/g')
    local escaped_checkpoint_folder=$(printf '%s\n' "$CHECKPOINT_FOLDER" | sed 's/[[\.*^$()+?{|]/\\&/g')
    local escaped_base_run_name=$(printf '%s\n' "$BASE_RUN_NAME" | sed 's/[[\.*^$()+?{|]/\\&/g')

    # Modify tokenizer_name
    sed -i "s|^tokenizer_name:.*|tokenizer_name: \"$escaped_tokenizer_name\"|" "$yaml_file"

    # Modify pretrained_model_name
    sed -i "s|^  pretrained_model_name:.*|  pretrained_model_name: \"$escaped_pretrained_model_name\"|" "$yaml_file"

    # Modify local_pretrain_checkpoint_folder
    sed -i "s|^local_pretrain_checkpoint_folder:.*|local_pretrain_checkpoint_folder: $escaped_checkpoint_folder|" "$yaml_file"

    # Modify base_run_name with task-specific suffix
    local task_specific_run_name="${BASE_RUN_NAME}-${task}-finetuning"
    sed -i "s|^base_run_name:.*|base_run_name: $task_specific_run_name|" "$yaml_file"

    # Handle starting_checkpoint_load_path based on task
    if [[ " ${MNLI_DEPENDENT_TASKS[@]} " =~ " ${task} " ]]; then
        # For mrpc, rte, stsb: use MNLI checkpoint
        local escaped_mnli_checkpoint=$(printf '%s\n' "$MNLI_CHECKPOINT" | sed 's/[[\.*^$()+?{|]/\\&/g')

        # First, remove any commented starting_checkpoint_load_path lines
        sed -i '/^# starting_checkpoint_load_path:/d' "$yaml_file"

        # Then, update or add the starting_checkpoint_load_path
        if grep -q "^starting_checkpoint_load_path:" "$yaml_file"; then
            sed -i "s|^starting_checkpoint_load_path:.*|starting_checkpoint_load_path: $escaped_mnli_checkpoint|" "$yaml_file"
        else
            # Add it after the local_pretrain_checkpoint_folder line
            sed -i "/^local_pretrain_checkpoint_folder:/a starting_checkpoint_load_path: $escaped_mnli_checkpoint" "$yaml_file"
        fi

        log_message "  Set starting_checkpoint_load_path to MNLI checkpoint for $task"
    else
        # For other tasks (including mnli): use the provided checkpoint path
        local escaped_checkpoint_path=$(printf '%s\n' "$CHECKPOINT_PATH" | sed 's/[[\.*^$()+?{|]/\\&/g')

        # First, remove any commented starting_checkpoint_load_path lines
        sed -i '/^# starting_checkpoint_load_path:/d' "$yaml_file"

        # Then, update or add the starting_checkpoint_load_path
        if grep -q "^starting_checkpoint_load_path:" "$yaml_file"; then
            sed -i "s|^starting_checkpoint_load_path:.*|starting_checkpoint_load_path: $escaped_checkpoint_path|" "$yaml_file"
        else
            # Add it after the local_pretrain_checkpoint_folder line
            sed -i "/^local_pretrain_checkpoint_folder:/a starting_checkpoint_load_path: $escaped_checkpoint_path" "$yaml_file"
        fi

        log_message "  Set starting_checkpoint_load_path to provided checkpoint for $task"
    fi

    log_message "  Modified YAML parameters for $task successfully"
    return 0
}

# Function to run a task with error handling and logging
run_task() {
    local task_file="$1"
    local task_name=$(basename "$task_file" .yaml)
    local start_time=$(date)
    local start_timestamp=$(date +%s)

    log_message "---------------------------------------------"
    log_message "Starting task: $task_name"
    log_message "Start time: $start_time"
    log_message "Task file: $task_file"
    log_message "---------------------------------------------"

    # Check if task file exists
    if [[ ! -f "$task_file" ]]; then
        local error_msg="ERROR: Task file '$task_file' not found"
        log_message "$error_msg"
        echo "FAILED,$task_name,$start_time,N/A,File not found" >> "${LOG_FILE%.txt}_summary.txt"
        return 1
    fi

    # Run the task and capture exit code
    if CUDA_VISIBLE_DEVICES="$CUDA_DEVICES" python glue.py "$task_file" 2>&1 | tee -a "$LOG_FILE"; then
        local end_time=$(date)
        local end_timestamp=$(date +%s)
        local duration=$((end_timestamp - start_timestamp))

        log_message "---------------------------------------------"
        log_message "COMPLETED task: $task_name"
        log_message "End time: $end_time"
        log_message "Duration: ${duration} seconds"
        log_message "---------------------------------------------"
        log_message ""

        # Log to summary file
        echo "COMPLETED,$task_name,$start_time,$end_time,${duration}s" >> "${LOG_FILE%.txt}_summary.txt"
        return 0
    else
        local end_time=$(date)
        local end_timestamp=$(date +%s)
        local duration=$((end_timestamp - start_timestamp))
        local exit_code=$?

        log_message "---------------------------------------------"
        log_message "FAILED task: $task_name"
        log_message "End time: $end_time"
        log_message "Duration: ${duration} seconds"
        log_message "Exit code: $exit_code"
        log_message "---------------------------------------------"
        log_message ""

        # Log to summary file
        echo "FAILED,$task_name,$start_time,$end_time,Exit code: $exit_code" >> "${LOG_FILE%.txt}_summary.txt"
        return $exit_code
    fi
}

# Determine which tasks to run
if [[ -n "$SELECTED_TASKS" ]]; then
    # Parse comma-separated tasks
    IFS=',' read -ra TASKS_TO_RUN <<< "$SELECTED_TASKS"
    log_message "Selected tasks: ${TASKS_TO_RUN[*]}"
else
    # Run all tasks
    TASKS_TO_RUN=("${ALL_TASKS[@]}")
    log_message "Running all available tasks: ${TASKS_TO_RUN[*]}"
fi

log_message ""

# Setup temporary directory and copy templates
setup_tmp_directory

# Modify YAML files with custom parameters
log_message "Modifying YAML files with custom parameters..."
modification_errors=0
for task in "${TASKS_TO_RUN[@]}"; do
    if ! modify_yaml_file "$task"; then
        log_message "ERROR: Failed to modify YAML for task: $task"
        ((modification_errors++))
    fi
done

if [[ $modification_errors -gt 0 ]]; then
    log_message "WARNING: $modification_errors YAML modification errors occurred"
    log_message "Continuing with execution..."
fi

log_message ""
log_message "YAML modification complete."
log_message ""

# Initialize summary file
echo "STATUS,TASK,START_TIME,END_TIME,DURATION_OR_ERROR" > "${LOG_FILE%.txt}_summary.txt"

# Counters for summary
completed_count=0
failed_count=0
failed_tasks=()

log_message "Starting sequential GLUE tasks execution..."
log_message ""

# Run each selected task in sequence
for task in "${TASKS_TO_RUN[@]}"; do
    task_file="${TMP_DIR}/${task}.yaml"

    if run_task "$task_file"; then
        ((completed_count++))
    else
        ((failed_count++))
        failed_tasks+=("$task")
        # Continue with next task even if current one fails
        log_message "Continuing with next task despite failure..."
        log_message ""
    fi
done

# Final summary
log_message "========================================="
log_message "EXECUTION SUMMARY"
log_message "========================================="
log_message "Total tasks attempted: $((completed_count + failed_count))"
log_message "Successfully completed: $completed_count"
log_message "Failed: $failed_count"

if [[ $failed_count -gt 0 ]]; then
    log_message "Failed tasks: ${failed_tasks[*]}"
fi

log_message "Finished at: $(date)"
log_message "========================================="
log_message ""
log_message "Detailed logs saved to: $LOG_FILE"
log_message "Summary saved to: ${LOG_FILE%.txt}_summary.txt"
log_message "Modified YAML files saved to: $TMP_DIR"

# Exit with appropriate code
if [[ $failed_count -gt 0 ]]; then
    exit 1
else
    exit 0
fi
