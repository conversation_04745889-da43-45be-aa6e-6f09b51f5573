#!/usr/bin/env python
"""
Cleanup script for shared memory segments used by the Streaming library.
This script can be run independently to clean up stale shared memory segments.
"""

import os
import subprocess
import tempfile
import sys
import argparse
import getpass

def run_sudo_command(command):
    """Run a command with sudo if available."""
    try:
        # Check if we have sudo access without password
        has_sudo_nopass = subprocess.call("sudo -n true", shell=True, stderr=subprocess.DEVNULL) == 0

        if has_sudo_nopass:
            # We have passwordless sudo access
            return subprocess.call(f"sudo {command}", shell=True)
        else:
            # We might have sudo access with password
            print(f"The following command requires sudo privileges:")
            print(f"  sudo {command}")
            print("Please enter your password if prompted.")
            return subprocess.call(f"sudo {command}", shell=True)

    except Exception as e:
        print(f"Error running sudo command: {e}")
        return 1

def cleanup_shared_memory(use_sudo=False, clean_mkl=False, clean_all=False):
    """Clean up any stale shared memory segments."""
    print("Cleaning up shared memory segments...")

    # Try to clean up in /dev/shm
    if os.path.exists("/dev/shm"):
        try:
            # List all files in /dev/shm
            files = os.listdir("/dev/shm")

            # Find cache_usage files
            cache_files = [f for f in files if f.endswith("cache_usage")]
            if cache_files:
                print(f"Found {len(cache_files)} cache_usage files in /dev/shm")

                if use_sudo:
                    # Use sudo to remove files
                    cache_pattern = " ".join([f"/dev/shm/{f}" for f in cache_files[:10]])
                    if len(cache_files) > 10:
                        cache_pattern = f"/dev/shm/*cache_usage"
                    run_sudo_command(f"rm -f {cache_pattern}")
                    print(f"Attempted to remove cache_usage files with sudo")
                else:
                    # Try to remove files without sudo
                    for file in cache_files:
                        try:
                            file_path = os.path.join("/dev/shm", file)
                            os.remove(file_path)
                            print(f"Removed {file_path}")
                        except (PermissionError, OSError) as e:
                            print(f"Could not remove {file}: {e}")

            # Find streaming files
            streaming_files = [f for f in files if f.startswith("000000_") or f.startswith("000001_")]
            if streaming_files:
                print(f"Found {len(streaming_files)} streaming files in /dev/shm")

                if use_sudo:
                    # Use sudo to remove files
                    streaming_pattern = " ".join([f"/dev/shm/{f}" for f in streaming_files[:10]])
                    if len(streaming_files) > 10:
                        streaming_pattern = f"/dev/shm/00000*_*"
                    run_sudo_command(f"rm -f {streaming_pattern}")
                    print(f"Attempted to remove streaming files with sudo")
                else:
                    # Try to remove files without sudo
                    for file in streaming_files:
                        try:
                            file_path = os.path.join("/dev/shm", file)
                            os.remove(file_path)
                            print(f"Removed {file_path}")
                        except (PermissionError, OSError) as e:
                            print(f"Could not remove {file}: {e}")

            # Find Intel MKL files if requested
            if clean_mkl or clean_all:
                mkl_files = [f for f in files if f.startswith("__KMP_REGISTERED_LIB_")]
                if mkl_files:
                    print(f"Found {len(mkl_files)} Intel MKL files in /dev/shm")

                    if use_sudo:
                        # Use sudo to remove files
                        run_sudo_command(f"rm -f /dev/shm/__KMP_REGISTERED_LIB_*")
                        print(f"Attempted to remove Intel MKL files with sudo")
                    else:
                        # Try to remove files without sudo
                        for file in mkl_files[:20]:  # Limit to 20 files for display
                            try:
                                file_path = os.path.join("/dev/shm", file)
                                os.remove(file_path)
                                print(f"Removed {file_path}")
                            except (PermissionError, OSError) as e:
                                print(f"Could not remove {file}: {e}")

                        if len(mkl_files) > 20:
                            print(f"... and {len(mkl_files) - 20} more MKL files")

            # Clean all files if requested
            if clean_all:
                current_user = getpass.getuser()
                user_files = [f for f in files if os.path.exists(f"/dev/shm/{f}") and
                             os.stat(f"/dev/shm/{f}").st_uid == os.getuid()]

                if user_files:
                    print(f"Found {len(user_files)} files owned by current user in /dev/shm")

                    for file in user_files[:20]:  # Limit to 20 files for display
                        try:
                            file_path = os.path.join("/dev/shm", file)
                            os.remove(file_path)
                            print(f"Removed {file_path}")
                        except (PermissionError, OSError) as e:
                            print(f"Could not remove {file}: {e}")

                    if len(user_files) > 20:
                        print(f"... and {len(user_files) - 20} more user files")

        except Exception as e:
            print(f"Error cleaning up /dev/shm: {e}")

    # Try to clean up in root directory (requires sudo)
    if use_sudo:
        try:
            print("Cleaning up shared memory files in root directory (requires sudo)...")
            run_sudo_command("rm -f /000000_* /_cache_usage /000000_cache_usage")
            print("Root directory cleanup completed")
        except Exception as e:
            print(f"Error during root cleanup: {e}")

    # Clean up in temp directory
    try:
        temp_dir = tempfile.gettempdir()
        streaming_dirs = [d for d in os.listdir(temp_dir) if d.startswith("streaming_shm_")]

        if streaming_dirs:
            print(f"Found {len(streaming_dirs)} streaming directories in {temp_dir}")
            for dir_name in streaming_dirs:
                dir_path = os.path.join(temp_dir, dir_name)
                try:
                    # Remove all files in the directory
                    for file in os.listdir(dir_path):
                        file_path = os.path.join(dir_path, file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            print(f"Removed {file_path}")

                    # Try to remove the directory
                    os.rmdir(dir_path)
                    print(f"Removed directory {dir_path}")
                except (PermissionError, OSError) as e:
                    print(f"Could not fully clean up {dir_path}: {e}")

    except Exception as e:
        print(f"Error cleaning up temp directories: {e}")

    print("Shared memory cleanup completed")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Clean up shared memory segments")
    parser.add_argument("--sudo", action="store_true", help="Use sudo to remove files (needed for files owned by other users)")
    parser.add_argument("--mkl", action="store_true", help="Clean up Intel MKL files (__KMP_REGISTERED_LIB_*)")
    parser.add_argument("--all", action="store_true", help="Clean up all files owned by current user")
    args = parser.parse_args()

    cleanup_shared_memory(use_sudo=args.sudo, clean_mkl=args.mkl, clean_all=args.all)

    print("\nTo run training with proper shared memory configuration, use:")
    print("./run_training.sh \"0,1,2,3\" yamls/main/flex-bert-rope-base-edu-fw-10B.yaml")

    if not args.sudo and not args.mkl:
        print("\nNote: To clean up files owned by other users, run:")
        print("python cleanup_shm.py --sudo")

    if not args.mkl and not args.all:
        print("\nNote: To clean up Intel MKL files, run:")
        print("python cleanup_shm.py --sudo --mkl")

    if args.sudo and args.mkl and not args.all:
        print("\nWarning: There are still many files in /dev/shm/")
        print("If you want to clean up all files owned by your user, run:")
        print("python cleanup_shm.py --all")
        print("\nIf you want to clean up ALL files (dangerous), you would need to run:")
        print("sudo rm -rf /dev/shm/*")
        print("WARNING: Only do this if you're sure no critical processes are using shared memory!")
