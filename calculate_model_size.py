#!/usr/bin/env python3
"""
Utility script to calculate model size from YAML configuration files.
This script replicates the model size calculation that's printed during training runs.

Usage:
    python calculate_model_size.py path/to/config.yaml
    python calculate_model_size.py yamls/main/playground/flex-bert-modernbert-base-edu-fw-fw2-50B-custom.yaml
"""

import sys
import argparse
from pathlib import Path
from typing import cast

from omegaconf import DictConfig, OmegaConf
from omegaconf import OmegaConf as om

# Import the same functions used in main.py
from main import build_model


def load_config(yaml_path: str) -> DictConfig:
    """Load configuration from YAML file, merging with defaults like main.py does."""
    yaml_path = Path(yaml_path)

    if not yaml_path.exists():
        raise FileNotFoundError(f"Config file not found: {yaml_path}")

    # Load defaults (same as main.py)
    with open("yamls/defaults.yaml") as f:
        default_cfg = om.load(f)

    # Load the specific config
    with open(yaml_path) as f:
        yaml_cfg = om.load(f)

    # Merge configs (same as main.py)
    cfg = om.merge(default_cfg, yaml_cfg)
    cfg = cast(DictConfig, cfg)

    return cfg


def calculate_model_size(cfg: DictConfig) -> tuple[int, int, int]:
    """
    Calculate model size from configuration.

    Returns:
        tuple: (total_params, trainable_params, model_method_params)
    """
    print("Building model from config...")
    print(f"Model type: {cfg.model.name}")

    # Build the model (same as main.py)
    model = build_model(cfg.model)

    # Calculate parameters using the same method as main.py
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    # Also try the model's built-in method if available
    model_method_params = None
    if hasattr(model, 'get_number_parameters'):
        model_method_params = model.get_number_parameters()
        print(f"Model's get_number_parameters(): {model_method_params:,}")
    elif hasattr(model, 'model') and hasattr(model.model, 'get_number_parameters'):
        model_method_params = model.model.get_number_parameters()
        print(f"Model's get_number_parameters(): {model_method_params:,}")

    return total_params, trainable_params, model_method_params


def main():
    parser = argparse.ArgumentParser(
        description="Calculate model size from YAML configuration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python calculate_model_size.py yamls/main/playground/flex-bert-modernbert-base-edu-fw-fw2-50B-custom.yaml
    python calculate_model_size.py yamls/main/flex-bert-base.yaml
        """
    )
    parser.add_argument(
        "config_path",
        help="Path to the YAML configuration file"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Print detailed model configuration"
    )

    args = parser.parse_args()

    try:
        # Load configuration
        cfg = load_config(args.config_path)

        if args.verbose:
            print("Model configuration:")
            print(om.to_yaml(cfg.model))
            print("-" * 50)

        # Calculate model size
        total_params, trainable_params, model_method_params = calculate_model_size(cfg)

        # Print results (same format as main.py)
        print(f"\nModel Size Summary:")
        print(f"Total parameters: {total_params:,} ({total_params:.4e})")
        print(f"Trainable parameters: {trainable_params:,} ({trainable_params:.4e})")

        if total_params != trainable_params:
            frozen_params = total_params - trainable_params
            print(f"Frozen parameters: {frozen_params:,} ({frozen_params:.4e})")

        # Convert to common size units
        param_size_mb = total_params * 4 / (1024 * 1024)  # Assuming float32 (4 bytes per param)
        param_size_gb = param_size_mb / 1024

        print(f"\nApproximate model size:")
        print(f"  {param_size_mb:.1f} MB (float32)")
        print(f"  {param_size_gb:.2f} GB (float32)")

    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
