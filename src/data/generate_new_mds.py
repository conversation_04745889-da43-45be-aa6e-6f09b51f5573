import argparse
import time
from datetime import datetime
import os
from torch.utils.data import DataLoader, IterableDataset

from data_utils import MDS_COLS_TEXT
from streaming import StreamingDataset, MDSWriter
# from utils import cleanup_shared_memory

from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict

class CombinedIterableDataset(IterableDataset):
    """Yield from two datasets in sequence: first a streaming MDS, then a map-style HF dataset."""
    def __init__(self, streaming_ds, map_ds):
        self.streaming_ds = streaming_ds
        self.map_ds = map_ds

    def __iter__(self):
        # first stream existing MDS
        for item in self.streaming_ds:
            yield item
        # then yield from the in-memory arrow dataset
        for item in self.map_ds:
            yield item

def count_records(output_path):
    """Count the number of records in the MDS dataset."""
    # Load the generated MDS dataset to count records
    sanity_check_ds = StreamingDataset(local=output_path, shuffle=False, split=None, batch_size=1)
    count = sum(1 for _ in sanity_check_ds)
    return count

def main():
    # 1) Log start time
    start_wall = time.time()
    start_dt = datetime.now()
    print(f"[{start_dt.isoformat()}] Starting conversion…")

    # 2) CLI arguments

    parser = argparse.ArgumentParser(
        description="Merge an existing MDS with an Arrow-based HF dataset into a new MDS."
    )
    parser.add_argument(
        "--mds_path",
        type=str,
        required=True,
        help="Directory of input .data/.idx MDS shards"
    )
    parser.add_argument(
        "--arrow_dir",
        type=str,
        required=True,
        help="Path to a local HuggingFace dataset or folder of .arrow files"
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory where merged MDS shards will be written"
    )
    parser.add_argument(
        "--batch_size",
        type=int,
        default=10,
        help="Batch size for reading both MDS and Arrow datasets"
    )
    parser.add_argument(
        "--num_workers",
        type=int,
        default=1,
        help="Number of DataLoader workers"
    )
    parser.add_argument(
        "--max_records",
        type=int,
        default=10_000_000_000,
        help="Stop after writing this many total records"
    )
    args = parser.parse_args()

    # 3) Load input dataset
    # --- load the Arrow dataset (map-style) ---
    try:
        hf_ds = load_dataset(args.arrow_dir)
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] load_dataset failed ({e}), falling back to load_from_disk")
        hf_ds = load_from_disk(args.arrow_dir)

    if isinstance(hf_ds, DatasetDict):
        # concatenate all splits into one map-style Dataset
        hf_ds = concatenate_datasets(list(hf_ds.values()))

    # --- load existing MDS as a streaming IterableDataset ---
    mds_ds = StreamingDataset(
        local=args.mds_path,
        shuffle=False,
        split=None,
        batch_size=args.batch_size
    )

    # --- chain them together ---
    combined = CombinedIterableDataset(mds_ds, hf_ds)

    # 4) Wrap in a DataLoader for batching & parallel I/O
    dataloader = DataLoader(
        combined,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        prefetch_factor=2 if args.batch_size <= 256 else 1
    )

    # 5) Write out with MDSWriter, stopping after max_records
    written = 0
    with MDSWriter(
        out=args.output_path,
        columns=MDS_COLS_TEXT,
        compression="zstd",
        progress_bar = True,
        exist_ok = True
    ) as writer:
        for batch in dataloader:
            # unpack the batch into a list of examples
            if args.batch_size == 1:
                items = [{k: v[0] for k, v in batch.items()}]
            else:
                batch_len = len(next(iter(batch.values())))
                items = [
                    {k: v[i] for k, v in batch.items()}
                    for i in range(batch_len)
                ]

            # write and check limit
            for item in items:
                writer.write(item)
                written += 1
                if written >= args.max_records:
                    print(f"[{datetime.now().isoformat()}] Reached {written} records; stopping early.")
                    break

            if written >= args.max_records:
                break

    # 6) Sanity check: count number of records in final MDS
    final_record_count = count_records(args.output_path)
    print(f"Sanity check: Counted {final_record_count} records in the final MDS dataset.")

    # 6) Cleanup and final logging
    # cleanup_shared_memory()
    end_dt = datetime.now()
    elapsed = time.time() - start_wall
    print(f"[{end_dt.isoformat()}] Finished writing {written} records to {args.output_path!r}")
    print(f"Total elapsed time: {elapsed:.2f}s ({elapsed/60:.2f}m)")

if __name__ == "__main__":
    main()
