# Note that some of the fields in this template haven't been filled in yet.
# Please resolve any `null` fields before launching!

# Follow the instructions in the README to set up ./my-copy-c4
# Or point data paths to your remote C4 dataset
data_local: /s1/7TB-1/mds_output/fineweb-edu-mds-split
data_remote: # If blank, files must be present in data_local

max_seq_len: 512
tokenizer_name: "intfloat/multilingual-e5-large-instruct" # switch to bert tokenizer until we add [MASK] token to the llama tokenizer meta-llama/Llama-2-7b-hf
mlm_probability: 0.3 # FlexBERT should use 30% masking for optimal performance

# Run Name
run_name: flex-bert-rope-base-fw-edu-10B-e5-tokenizer

# Model
model:
  name: flex_bert
  recompute_metric_loss: false # recompute metric loss, use if passing label_smoothing to record non-label-smoothed loss as a metric
  pretrained_model_name: ${tokenizer_name}
  tokenizer_name: ${tokenizer_name}
  # FlexBERT 'base' generally uses the default architecture values from the Hugging Face BertConfig object
  # Note: if using the pretrained_checkpoint argument to create a model from an existing checkpoint, make sure
  # the model_config settings match the architecture of the existing model
  model_config:
    num_attention_heads: 12 # bert-base default
    num_hidden_layers: 12 # bert-base default
    hidden_size: 384
    intermediate_size: 1536
    attention_layer: rope
    attention_probs_dropout_prob: 0.0
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: mlp
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
    padding: unpadded
    sparse_prediction: false
    rotary_emb_dim: null # will be set to headdim by default
    rotary_emb_base: 10000.0
    rotary_emb_scale_base: null
    rotary_emb_interleaved: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: False
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    sliding_window: 128
    global_attn_every_n_layers: 3
    unpad_embeddings: true
    pad_logits: false
    allow_embedding_resizing: true

# Dataloaders
train_loader:
  name: text
  dataset:
    streaming: false
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
  drop_last: true
  num_workers: 8

eval_loader:
  name: text
  dataset:
    streaming: false
    local: ${data_local}
    remote: ${data_remote}
    split: validation
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: false
    mlm_probability: 0.15 # We always evaluate at 15% masking for consistent comparison
  drop_last: false
  num_workers: 8

# Optimization
scheduler:
  name: linear_decay_with_warmup
  t_warmup: 0.06dur # Warmup to the full LR for 6% of the training duration
  alpha_f: 0.02 # Linearly decay to 0.02x the full LR by the end of the training duration

optimizer:
  name: decoupled_adamw
  lr: 5.0e-4 # Peak learning rate
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-5 # Amount of weight decay regularization
  filter_bias_norm_wd: true # If True, doesn't apply weight decay to norm layers and biases

# algorithms:

max_duration: 1ep # 286720000sp # Subsample the training data for ~275M samples
eval_interval: 5000ba

global_train_batch_size: 1024 # reduced from 4096
device_train_microbatch_size: 32 # reduced from 128

# System
seed: 17
# device_train_microbatch_size: auto
precision: amp_bf16

global_eval_batch_size: 256
device_eval_microbatch_size: 64
eval_subset_num_batches: 1024  # Only evaluate on 500 batches instead of all 3,779

# Logging
python_log_level: "DEBUG" 
progress_bar: true
log_to_console: true
console_log_interval: 1ba

algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

callbacks:
  speed_monitor:
    window_size: 10
  lr_monitor: {}
  memory_monitor: {}  # Add memory monitoring
  optimizer_monitor: {}  # Add optimizer monitoring
  dataloader_speed: {}  # Add dataloader speed monitoring

loggers:
  tensorboard:
    log_dir: ~/tb_runs/${run_name}  # Directory for TensorBoard logs
    flush_interval: 100                      # How frequently to flush logs (in batches)

  wandb:
    project: Modernbert_Pretrain  # e.g., "bert-pretraining"
#     # entity: sutkaggles         # your wandb username

# (Optional) Checkpoint to local filesystem or remote object store
save_interval: 1000ba
save_num_checkpoints_to_keep: 1  # Important, this cleans up checkpoints saved to DISK
save_folder:  ./ckpt/{run_name}/    # e.g. './{run_name}/ckpt' (local) or 's3://mybucket/mydir/{run_name}/ckpt' (remote)
debug: true
# (Optional) Load from local filesystem or remote object store to
# start from an existing model checkpoint;
# e.g. './ckpt/latest-rank{rank}.pt' (local), or
# 's3://mybucket/mydir/ckpt/latest-rank{rank}.pt' (remote)
# load_path: null
