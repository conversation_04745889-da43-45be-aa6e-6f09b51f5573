# Whether to run the various GLUE jobs serially or in parallel (use parallel=True to take advantage of multiple GPUs)
parallel: true

# Basic run configuration, additional details will be added to this name for each GLUE task, and each random seed
base_run_name: bert-base-uncased-glue-finetuning
default_seed: 19
precision: amp_bf16

# Tokenizer for dataset creation
tokenizer_name: bert-base-uncased

# Base model config
model:
  name: hf_bert
  use_pretrained: true
  pretrained_model_name: ${tokenizer_name}
  tokenizer_name: ${tokenizer_name}

# Saving
save_finetune_checkpoint_prefix: ./bert-finetune-checkpoints
save_finetune_checkpoint_folder: ${save_finetune_checkpoint_prefix}/${base_run_name}

# (Optional) W&B logging
# loggers:
  # wandb:
    # project: # Fill this in if using W&B
    # entity: # Fill this in if using W&B

# Callbacks
callbacks:
  lr_monitor: {}
  speed_monitor: {}

# Scheduler
scheduler:
  name: linear_decay_with_warmup
  t_warmup: 0.06dur
  alpha_f: 0.0

# Task configuration
tasks:
  mnli:
    # Specify any extra task-specific arguments for the trainer here
    trainer_kwargs:
      # We keep one MNLI checkpoint locally so that we can start finetuning of
      # RTE, MRPC and STS-B from the MNLI checkpoint
      save_num_checkpoints_to_keep: 1
  rte:
    seeds: [19, 8364, 717, 10536, 90166]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  qqp:
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  qnli:
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  sst2:
    seeds: [19, 8364, 717]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  stsb:
    seeds: [19, 8364, 717, 10536, 90166]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  mrpc:
    seeds: [19, 8364, 717, 10536, 90166]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
  cola:
    seeds: [19, 8364, 717, 10536]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 0
