# COLA task finetuning configuration

# Base template for individual GLUE task finetuning

# Whether to run the various GLUE jobs serially or in parallel (use parallel=True to take advantage of multiple GPUs)
parallel: true

# Basic run configuration
default_seed: 19
precision: amp_bf16

# Tokenizer for dataset creation
tokenizer_name: "/home/<USER>/notebooks/notebooks/spm_tokenizer_15mfa30men_65536"

# Base model config
model:
  name: flex_bert
  recompute_metric_loss: false # recompute metric loss, use if passing label_smoothing to record non-label-smoothed loss as a metric
  pretrained_model_name: bert-base-uncased
  tokenizer_name: ${tokenizer_name}
  # FlexBERT 'base' generally uses the default architecture values from the Hugging Face BertConfig object
  # Note: if using the pretrained_checkpoint argument to create a model from an existing checkpoint, make sure
  # the model_config settings match the architecture of the existing model
  model_config:
    num_attention_heads: 12 # bert-base default
    num_hidden_layers: 12 # bert-base default
    attention_layer: rope  # Using RoPE for alternating local/global attention
    attention_probs_dropout_prob: 0.0
    # Disable bias terms in all linear layers except final decoder
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu  # Using GLU (GeGLU-like) activation as specified
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
      bias: false  # Disable bias in LayerNorms
    padding: padded
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    # Alternating local/global attention settings
    sliding_window: 128  # 128 token local sliding window
    global_attn_every_n_layers: 3  # Every third layer uses global attention
    rotary_emb_base: 160000.0  # RoPE theta for global attention layers
    local_attn_rotary_emb_base: 10000.0  # RoPE theta for local attention layers
    unpad_embeddings: false
    pad_logits: false
    # Enable bias only for final decoder layer
    decoder_bias: true
    allow_embedding_resizing: true

# Loading
# (fill this in with the composer checkpoint from the end of pre-training a Mosaic BERT)
starting_checkpoint_load_path: /home/<USER>/notebooks/ModernBert_pretrain/ckpt/flex-bert-rope-robertxlml-fw-edu-10B-custom-tokenizer2/ep1-ba8500-rank0.pt
local_pretrain_checkpoint_folder: /home/<USER>/notebooks/ModernBert_pretrain/ckpt/flex-bert-rope-robertxlml-fw-edu-10B-custom-tokenizer2

# Saving
save_finetune_checkpoint_prefix: ./local-finetune-checkpoints/ # (local)
# save_finetune_checkpoint_prefix: s3://<bucket>/remote-finetune-checkpoints # (remote)

# # TensorBoard logging
# loggers:
#   tensorboard:
#     log_dir: ~/tb_runs/${base_run_name}
#     flush_interval: 100

# Callbacks
callbacks:
  lr_monitor: {}
  speed_monitor: {}

# Scheduler
scheduler:
  name: linear_decay_with_warmup
  t_warmup: 0.06dur
  alpha_f: 0.0

# Results output file
# results_file: ./results/glue_results.txt

# Task-specific run name
base_run_name: flex-roberta-custom-tokenizer-cola-finetuning

# Save folder specific to this task
save_finetune_checkpoint_folder: ${save_finetune_checkpoint_prefix}/${base_run_name}

# Task-specific results file
results_file: ./results-custom-tokenizer2/${base_run_name}.txt

# Task configuration
tasks:
  cola:
    seeds: [19, 8364, 717, 10536]
    trainer_kwargs:
      save_num_checkpoints_to_keep: 1
      # COLA-specific hyperparameters
      max_duration: "10ep"
      batch_size: 32
      eval_interval: "250ba"
