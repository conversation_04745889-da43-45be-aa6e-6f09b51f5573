# Base template for individual GLUE task finetuning

# Whether to run the various GLUE jobs serially or in parallel (use parallel=True to take advantage of multiple GPUs)
parallel: true

# Basic run configuration
default_seed: 19
precision: amp_bf16

# Tokenizer for dataset creation
tokenizer_name: bert-base-uncased

# Base model config
model:
  name: flex_bert
  pretrained_model_name: ${tokenizer_name}
  tokenizer_name: ${tokenizer_name}
  model_config:
    deterministic_fa2: true

# Loading
# (fill this in with the composer checkpoint from the end of pre-training a Mosaic BERT)
starting_checkpoint_load_path: /home/<USER>/notebooks/ModernBert_pretrain/ckpt/flex-bert-rope-base-fw-edu-10B/ep1-ba8500-rank0.pt
local_pretrain_checkpoint_folder: /home/<USER>/notebooks/ModernBert_pretrain/ckpt/flex-bert-rope-base-fw-edu-10B/

# Saving
save_finetune_checkpoint_prefix: ./local-finetune-checkpoints/ # (local)
# save_finetune_checkpoint_prefix: s3://<bucket>/remote-finetune-checkpoints # (remote)

# TensorBoard logging
loggers:
  tensorboard:
    log_dir: ~/tb_runs/${base_run_name}
    flush_interval: 100

# Callbacks
callbacks:
  lr_monitor: {}
  speed_monitor: {}

# Scheduler
scheduler:
  name: linear_decay_with_warmup
  t_warmup: 0.06dur
  alpha_f: 0.0

# Results output file
results_file: ./results/glue_results.txt
