# GLUE Task-Specific YAML Templates

This directory contains individual YAML configuration files for each GLUE task. These templates allow you to run each GLUE task independently, making it easier to:

1. Handle interruptions during training
2. Track progress for each task separately
3. Customize hyperparameters for specific tasks
4. Run tasks in parallel on different machines or GPUs

## Available Task Templates

- `mnli.yaml`: MNLI task configuration
- `rte.yaml`: RTE task configuration
- `qqp.yaml`: QQP task configuration
- `qnli.yaml`: QNLI task configuration
- `sst2.yaml`: SST2 task configuration
- `stsb.yaml`: STSB task configuration
- `mrpc.yaml`: MRPC task configuration
- `cola.yaml`: COLA task configuration

## How to Use

To run a specific GLUE task, use the following command:

```bash
python glue.py yamls/finetuning/glue/task_templates/<task_name>.yaml
```

For example, to run the MNLI task:

```bash
python glue.py yamls/finetuning/glue/task_templates/mnli.yaml
```

## Task Dependencies

Some GLUE tasks benefit from starting with a checkpoint from another task. For example:

- RTE, MRPC, and STSB typically start from an MNLI checkpoint
- CO<PERSON> typically starts from a SWAG checkpoint

In these templates, there are commented lines for specifying the starting checkpoint. After running the prerequisite task (e.g., MNLI), uncomment and update the path in the dependent task's YAML file.

## TensorBoard Logging

All templates include TensorBoard logging configuration. Logs will be saved to:

```
~/tb_runs/<base_run_name>
```

To view the logs, run:

```bash
tensorboard --logdir=~/tb_runs
```

## Customization

Each task template inherits from `base_template.yaml`, which contains common settings. You can modify:

1. The base template to change settings for all tasks
2. Individual task templates to customize specific tasks

## Checkpoints

All templates are configured to save at least one checkpoint during training. Checkpoints are saved to:

```
./local-finetune-checkpoints/<base_run_name>/task=<task_name>/seed=<seed>/
```

## Results Output

Each task is configured to save its results to a text file. Results are saved to:

```
./results/<base_run_name>.txt
```

The results file includes:
- A timestamp of when the training was run
- Training time information
- Detailed metrics for each task and seed
- Average metrics across seeds

This ensures that even if the process is interrupted, you'll have a record of the results up to that point.
