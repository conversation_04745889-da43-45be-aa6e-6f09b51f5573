# MDS Format Explanation and Usage Guide

## What is MDS?

MDS (Mosaic Dataset Streaming) is a file format developed by MosaicML (now part of Databricks) for efficient storage and streaming of large datasets. It's designed to optimize training large models by providing:

1. **Efficient Storage**: Stores data in a columnar format that can be efficiently read and processed
2. **Streaming Capability**: Allows data to be streamed during training without loading the entire dataset into memory
3. **Compression Support**: Supports various compression algorithms to reduce storage requirements
4. **Distributed Training Support**: Designed to work well with distributed training setups

## MDS vs MDS.zstd

There are two main variants of MDS files:

1. **MDS (uncompressed)**:
   - Raw MDS format without compression
   - Faster to read but takes more storage space
   - Useful for datasets that are frequently accessed and where storage space is not a concern

2. **MDS.zstd (compressed)**:
   - MDS format compressed with the ZSTD compression algorithm
   - Takes less storage space but requires decompression during reading
   - The script uses this format by default (see the `compression='zstd'` parameter in the `MDSWriter`)
   - Provides a good balance between storage efficiency and read performance

In most cases, the compressed MDS.zstd format is recommended as it significantly reduces storage requirements while maintaining good read performance.

## Using the Modified Script

### Single Dataset Conversion (Backward Compatible)

```bash
python src/data/local_hf_to_mds.py \
    -o /path/to/output \
    -r huggingface_dataset_name \
    -s split_name \
    -c config_name \
    -t text_column_name \
    -p num_processors
```

### Multiple Datasets Conversion

1. Create a JSON file with dataset information:

```json
[
  {
    "path": "dataset_path_1",
    "text_column": "text",
    "split": "train",
    "config": null
  },
  {
    "path": "dataset_path_2",
    "text_column": "content",
    "split": "train",
    "config": null
  }
]
```

For testing purposes, you can limit the number of records processed from each dataset:

```json
[
  {
    "path": "dataset_path_1",
    "text_column": "text",
    "split": "train",
    "config": null,
    "num_records": 1000
  },
  {
    "path": "dataset_path_2",
    "text_column": "content",
    "split": "train",
    "config": null,
    "num_records": 1000
  }
]
```

2. Run the script with the JSON file:

```bash
python src/data/local_hf_to_mds.py \
    -o /path/to/output \
    -d path_to_datasets_json \
    -p 32 \
    -b 128 \
    --shuffle_seed 42
```

## Parameters Explanation

- `-o, --output_path`: Path where the MDS dataset will be saved
- `-d, --datasets_json`: Path to JSON file containing dataset information
- `-r, --repo_name`: HuggingFace dataset repository name (for single dataset conversion)
- `-s, --split`: Dataset split to convert (e.g., 'train', 'validation')
- `-c, --config`: Dataset configuration name
- `-t, --text_column`: Name of the column containing text to convert (default: 'text')
- `-p, --num_proc`: Number of processes to use (default: num_cpu_cores // 2)
- `--shuffle_seed`: Random seed for shuffling the combined dataset (default: 42)
- `-b, --batch_size`: Batch size for processing data (default: 1)
  - Higher values (64-256) can improve throughput with high-bandwidth connections
  - For very large datasets on fast networks, try 512-1024
  - Optimal value depends on available RAM and network bandwidth

## JSON File Format

The JSON file should contain an array of objects, where each object has the following fields:

- `path` (required): Path to the dataset (local path or HuggingFace dataset name)
- `text_column` (required): Name of the column containing text to convert
- `split` (optional): Dataset split to convert (e.g., 'train', 'validation')
- `config` (optional): Dataset configuration name
- `num_records` (optional): Limit the number of records to process from this dataset (useful for testing)

## Working with MDS Files

After conversion, you can use the MDS dataset with libraries that support the format:

```python
from streaming import StreamingDataset

# Load the dataset
dataset = StreamingDataset(local="/path/to/mds/dataset",
                          shuffle=True,
                          batch_size=32)

# Iterate through the dataset
for batch in dataset:
    # Process batch
    pass
```

## Benefits of Using MDS for Large Language Models

1. **Memory Efficiency**: Streams data from disk instead of loading everything into memory
2. **Training Speed**: Optimized for fast data loading during training
3. **Storage Efficiency**: Compressed format reduces storage requirements
4. **Scalability**: Works well with distributed training across multiple GPUs/nodes

## Working with Multilingual Datasets

When combining datasets in different languages (e.g., Persian and English), proper shuffling is important to ensure good mixing of the content. The script now includes explicit shuffling after combining datasets to ensure better language mixing.

### Dataset Concatenation Behavior

When combining multiple datasets, the script uses the `concatenate_datasets` function. This approach:

1. Appends all datasets together sequentially
2. Includes all records from all datasets in the final output
3. Avoids creating cache files in the original dataset directories
4. Is followed by shuffling to ensure good mixing of content

For example, if you combine:
- Dataset A with 20,000 records
- Dataset B with 10,000 records

The final dataset will contain all 30,000 records (20,000 from A and 10,000 from B), concatenated and then shuffled to ensure good mixing.

### Controlling Shuffling

You can control the shuffling behavior with the `--shuffle_seed` parameter:

```bash
python src/data/local_hf_to_mds.py \
    -o /path/to/output \
    -d multilingual_datasets.json \
    -p 8 \
    --shuffle_seed 42
```

- Using the same seed value will produce the same shuffling pattern each time
- Different seed values will produce different shuffling patterns
- This is useful for reproducibility or for creating different mixtures of the same datasets

### Optimizing Performance for Large Datasets

When working with very large datasets (hundreds of millions of records), optimizing the processing parameters becomes crucial:

#### Optimal Number of Processors (`-p, --num_proc`)

For I/O bound operations like dataset processing, the optimal number of processors is often much lower than the total available CPU cores:

- For datasets on local SSD storage: Try 16-32 processors
- For datasets on network storage (SSHFS): Try 32-48 processors
- For datasets on high-performance storage systems: Try 48-64 processors

The relationship between dataset size and optimal processor count is not linear. For very large datasets, the I/O bottleneck often becomes more significant.

#### Optimal Batch Size (`-b, --batch_size`)

The batch size parameter can significantly impact performance, especially with high-bandwidth connections:

- For 1Gbps network connections: Try batch sizes of 64-128
- For 10Gbps network connections: Try batch sizes of 256-512
- For local SSD storage: Try batch sizes of 128-256
- For very large datasets (>100M records): Try batch sizes of 512-1024

#### Finding the Optimal Configuration

For the best performance with your specific hardware, try running benchmarks with different combinations:

```bash
# Example benchmark script
for np in 16 32 48 64; do
  for bs in 64 128 256 512; do
    echo "Testing with num_proc=${np}, batch_size=${bs}"
    time python src/data/local_hf_to_mds.py \
      -o /tmp/benchmark_output_${np}_${bs} \
      -d sample_datasets.json \
      -p ${np} \
      -b ${bs}
    rm -rf /tmp/benchmark_output_${np}_${bs}
  done
done
```
