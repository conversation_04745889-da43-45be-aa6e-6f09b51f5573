# Shared Memory Management for ModernBert Training

This document explains how to handle shared memory issues that may occur when running distributed training with the ModernBert codebase.

## Problem Description

When running distributed training with multiple GPUs using commands like:

```bash
CUDA_VISIBLE_DEVICES=0,1,2,3 composer main.py yamls/main/flex-bert-rope-base-edu-fw-10B.yaml
```

You may encounter permission errors related to shared memory files:

```
[rank3]: FileExistsError: [Errno 17] File exists: '/000000_cache_usage'
[rank3]: PermissionError: [Errno 13] Permission denied: '/000000_cache_usage'
```

These errors occur because:

1. The MosaicML Streaming library creates shared memory files in the root directory (`/`) or `/dev/shm/`
2. Regular users don't have permission to create or access files in the root directory
3. Files in `/dev/shm/` may be owned by other users, causing permission issues

## Solution Overview

The `cleanup_shm.py` script helps address these issues by cleaning up stale shared memory segments that might be causing conflicts or permission problems.

### Cleaning Up Shared Memory

If you encounter shared memory issues, you can use the cleanup script:

```bash
# Basic cleanup (only files you own)
./cleanup_shm.py

# Cleanup with sudo (for files owned by other users)
./cleanup_shm.py --sudo

# Cleanup Intel MKL files (__KMP_REGISTERED_LIB_*)
./cleanup_shm.py --sudo --mkl

# Cleanup all files owned by your user
./cleanup_shm.py --all
```

## Common Issues and Solutions

### Large Number of Files in /dev/shm/

If you see a large number of files in `/dev/shm/` (especially `__KMP_REGISTERED_LIB_*` files), you can clean them up:

```bash
# Safe cleanup of Intel MKL files
sudo ./cleanup_shm.py --sudo --mkl

# Last resort (if you're sure it's safe)
sudo rm -f /dev/shm/__KMP_REGISTERED_LIB_*
```

### Other Common Errors

You might encounter other errors related to shared memory, such as:

```
OSError: [Errno 40] Too many levels of symbolic links: '/000000_barrier'
```

These errors often indicate issues with how shared memory is being managed. Running the cleanup script before training can help prevent these errors.

### Permission Denied Errors

If you still get permission errors:

1. Make sure you're running the cleanup script with sudo: `python cleanup_shm.py --sudo --mkl`
2. Try running with fewer GPUs (e.g., `CUDA_VISIBLE_DEVICES=0,1 composer main.py ...`)
3. Check if any shared memory files are still present: `ls -la /dev/shm/`
4. As a last resort, consider rebooting the system

### How the Cleanup Script Works

The cleanup script:

1. Identifies shared memory files in `/dev/shm/` and other locations
2. Removes stale shared memory segments that might be causing conflicts
3. Can clean up Intel MKL files that accumulate over time
4. Provides options to clean up files owned by other users (with sudo)

This helps prevent permission issues and conflicts when running distributed training.

## Prevention for the Future

To prevent shared memory issues in the future:

1. Run the cleanup script before starting training:
   ```bash
   python cleanup_shm.py --sudo --mkl
   ```

2. Periodically clean up shared memory files

3. Consider setting up a cron job for regular cleanup:
   ```
   0 2 * * * find /dev/shm -name "__KMP_REGISTERED_LIB_*" -mtime +1 -delete
   ```

4. Set these environment variables before running training to reduce shared memory usage:
   ```bash
   export KMP_AFFINITY=disabled
   export MKL_THREADING_LAYER=sequential
   ```

## Technical Details

The shared memory issues occur because:

1. The MosaicML Streaming library uses shared memory for efficient data sharing between processes
2. Intel's Math Kernel Library (MKL) creates temporary files in `/dev/shm/`
3. When distributed training runs with multiple GPUs, each process creates its own shared memory segments

Running the cleanup script helps remove stale shared memory segments that might be causing conflicts or permission issues.
