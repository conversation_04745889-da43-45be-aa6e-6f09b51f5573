# Required parameters
tokenizer_name: "intfloat/multilingual-e5-large-instruct"
pretrained_model_name: bert-base-uncased
checkpoint_path: "./ckpt/flex-bert-modernbert-base-edu-fw_fw2-50B-e5/ep1-ba64077-rank0.pt"
checkpoint_folder: "./ckpt/flex-bert-modernbert-base-edu-fw_fw2-50B-e5"
base_run_name: "flex-bert-50B-e5-tokenizer"

# Optional parameters
# mnli_checkpoint: "./local-finetune-checkpoints/..."
tasks: "mnli,stsb,mrpc,rte"
# log_file: "my_glue_tasks_log.txt"
# cuda_devices: "6,7"
